#!/usr/bin/env python3
"""
对比不同LOO方法的性能和效果
"""

import subprocess
import time
import os
import pandas as pd

def run_experiment(name, cmd, timeout=600):
    """运行实验"""
    print(f"\n{'='*60}")
    print(f"Running: {name}")
    print(f"Command: {cmd}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ {name} completed in {duration:.1f} seconds")
            return True, duration, result.stdout
        else:
            print(f"❌ {name} failed with return code {result.returncode}")
            print("Error:", result.stderr[:500])
            return False, duration, None
            
    except subprocess.TimeoutExpired:
        end_time = time.time()
        duration = end_time - start_time
        print(f"⏰ {name} timed out after {duration:.1f} seconds")
        return False, duration, None
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"💥 {name} failed with exception: {e}")
        return False, duration, None

def find_latest_result():
    """找到最新的结果文件"""
    result_dir = "result/project"
    if not os.path.exists(result_dir):
        return None
    
    group_dirs = [d for d in os.listdir(result_dir) if d.startswith("group_")]
    if not group_dirs:
        return None
    
    group_nums = [int(d.split("_")[1]) for d in group_dirs if d.split("_")[1].isdigit()]
    if not group_nums:
        return None
    
    latest_group = f"group_{max(group_nums)}"
    result_file = os.path.join(result_dir, latest_group, "results.csv")
    
    return result_file if os.path.exists(result_file) else None

def parse_results(result_file):
    """解析结果"""
    if not result_file or not os.path.exists(result_file):
        return None
    
    try:
        df = pd.read_csv(result_file)
        mean_row = df[df['Row Names'] == 'Mean']
        if not mean_row.empty:
            return {
                'image_auroc': float(mean_row['image_auroc'].iloc[0]),
                'pixel_auroc': float(mean_row['pixel_auroc'].iloc[0])
            }
    except Exception as e:
        print(f"Error parsing results: {e}")
    
    return None

def main():
    """主函数"""
    
    base_cmd = "python main.py --dataset visa --data_path ./visa --noise 0.08 --overlap --seed 0 --gpu 0 --resize 512 --imagesize 512 --sampling_ratio 0.01 --subdatasets candle"
    
    experiments = [
        {
            "name": "Baseline (No LOO)",
            "cmd": base_cmd,
            "timeout": 300
        },
        {
            "name": "Fast LOO (similarity_threshold=0.1)",
            "cmd": base_cmd + " --exclude_self --fast_loo --similarity_threshold 0.1",
            "timeout": 400
        },
        {
            "name": "Fast LOO (similarity_threshold=0.2)",
            "cmd": base_cmd + " --exclude_self --fast_loo --similarity_threshold 0.2",
            "timeout": 400
        }
    ]
    
    results = {}
    
    for exp in experiments:
        success, duration, output = run_experiment(exp["name"], exp["cmd"], exp["timeout"])
        
        if success:
            result_file = find_latest_result()
            parsed = parse_results(result_file)
            
            if parsed:
                results[exp["name"]] = {
                    **parsed,
                    'duration': duration
                }
            else:
                print(f"⚠️  Could not parse results for {exp['name']}")
        else:
            print(f"❌ {exp['name']} failed")
    
    # 打印对比结果
    print("\n" + "="*80)
    print("COMPARISON RESULTS")
    print("="*80)
    
    if results:
        print(f"{'Method':<35} {'Image AUROC':<12} {'Pixel AUROC':<12} {'Time(s)':<8} {'Improvement':<12}")
        print("-" * 80)
        
        baseline_auc = None
        
        for name, result in results.items():
            img_auc = result['image_auroc']
            pixel_auc = result['pixel_auroc']
            duration = result['duration']
            
            if "Baseline" in name:
                baseline_auc = img_auc
                improvement = "Baseline"
            else:
                if baseline_auc is not None:
                    improvement = f"+{img_auc - baseline_auc:.4f}"
                else:
                    improvement = "N/A"
            
            print(f"{name:<35} {img_auc:<12.4f} {pixel_auc:<12.4f} {duration:<8.1f} {improvement:<12}")
    
    else:
        print("No successful experiments to compare!")
    
    print("="*80)

if __name__ == "__main__":
    main()
