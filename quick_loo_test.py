#!/usr/bin/env python3
"""
快速测试LOO-Coreset功能
"""

import os
import sys
import subprocess

def run_single_test():
    """运行单个测试来验证LOO功能"""
    
    # 测试命令：使用overlap模式和LOO
    cmd = [
        "python", "main.py",
        "--dataset", "visa",
        "--data_path", "./visa", 
        "--noise", "0.08",
        "--overlap",  # 关键：启用overlap模式
        "--exclude_self",  # 关键：启用LOO
        "--similarity_threshold", "0.1",
        "--seed", "0",
        "--gpu", "0",
        "--resize", "512",
        "--imagesize", "512", 
        "--sampling_ratio", "0.01",
        "--subdatasets", "candle"
    ]
    
    print("Testing LOO-Coreset with command:")
    print(" ".join(cmd))
    print("\nRunning...")
    
    try:
        # 运行命令
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)
        
        if result.returncode == 0:
            print("✅ Test completed successfully!")
            
            # 查找输出中的关键信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if "LOO-Coreset enabled" in line:
                    print(f"✅ {line}")
                elif "Using True LOO-Coreset prediction" in line:
                    print(f"✅ {line}")
                elif "image_auroc" in line:
                    print(f"📊 {line}")
                elif "pixel_auroc" in line:
                    print(f"📊 {line}")
            
            # 检查是否有错误信息
            if result.stderr:
                print("\n⚠️  Stderr output:")
                print(result.stderr)
                
        else:
            print(f"❌ Test failed with return code: {result.returncode}")
            print("Stdout:", result.stdout)
            print("Stderr:", result.stderr)
            
    except subprocess.TimeoutExpired:
        print("❌ Test timed out after 30 minutes")
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")

def check_implementation():
    """检查LOO实现是否正确"""
    print("Checking LOO-Coreset implementation...")
    
    # 检查关键文件是否存在
    files_to_check = [
        "src/softpatch.py",
        "main.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            return False
    
    # 检查关键函数是否存在
    try:
        with open("src/softpatch.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        required_functions = [
            "_build_loo_memory_bank",
            "_predict_with_loo", 
            "_predict_with_true_loo",
            "set_test_image_mapping"
        ]
        
        for func in required_functions:
            if func in content:
                print(f"✅ Function {func} found")
            else:
                print(f"❌ Function {func} missing")
                return False
                
    except Exception as e:
        print(f"❌ Error checking implementation: {e}")
        return False
    
    print("✅ Implementation check passed!")
    return True

def main():
    print("="*60)
    print("LOO-CORESET QUICK TEST")
    print("="*60)
    
    # 首先检查实现
    if not check_implementation():
        print("❌ Implementation check failed. Please fix the issues first.")
        return
    
    print("\n" + "="*60)
    print("RUNNING FUNCTIONAL TEST")
    print("="*60)
    
    # 运行功能测试
    run_single_test()
    
    print("\n" + "="*60)
    print("TEST COMPLETED")
    print("="*60)

if __name__ == "__main__":
    main()
