#!/usr/bin/env python3
"""
运行LOO对比实验
"""

import subprocess
import time
import os
import pandas as pd

def run_single_experiment(name, cmd):
    """运行单个实验"""
    print(f"\n{'='*50}")
    print(f"Running: {name}")
    print(f"Command: {cmd}")
    print('='*50)
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=600)
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ {name} completed in {duration:.1f} seconds")
            
            # 查找关键输出信息
            lines = result.stdout.split('\n')
            for line in lines:
                if "Simple LOO enabled" in line:
                    print(f"📋 {line}")
                elif "Using Simple LOO-Coreset prediction" in line:
                    print(f"🔄 {line}")
                elif "image_auroc" in line and "Mean" not in line:
                    print(f"📊 {line}")
            
            return True, duration, result.stdout
        else:
            print(f"❌ {name} failed")
            print("Error:", result.stderr[:200])
            return False, duration, None
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {name} timed out")
        return False, 600, None
    except Exception as e:
        print(f"💥 {name} failed: {e}")
        return False, 0, None

def find_latest_result():
    """找到最新结果文件"""
    result_dir = "result/project"
    if not os.path.exists(result_dir):
        return None
    
    group_dirs = [d for d in os.listdir(result_dir) if d.startswith("group_")]
    if not group_dirs:
        return None
    
    group_nums = [int(d.split("_")[1]) for d in group_dirs if d.split("_")[1].isdigit()]
    if not group_nums:
        return None
    
    latest_group = f"group_{max(group_nums)}"
    result_file = os.path.join(result_dir, latest_group, "results.csv")
    
    return result_file if os.path.exists(result_file) else None

def parse_result(result_file):
    """解析结果文件"""
    if not result_file or not os.path.exists(result_file):
        return None
    
    try:
        df = pd.read_csv(result_file)
        mean_row = df[df['Row Names'] == 'Mean']
        if not mean_row.empty:
            return {
                'image_auroc': float(mean_row['image_auroc'].iloc[0]),
                'pixel_auroc': float(mean_row['pixel_auroc'].iloc[0])
            }
    except Exception as e:
        print(f"Error parsing {result_file}: {e}")
    
    return None

def main():
    """主函数"""
    
    base_cmd = "python main.py --dataset visa --data_path ./visa --noise 0.08 --seed 0 --gpu 0 --resize 512 --imagesize 512 --sampling_ratio 0.01 --subdatasets candle"
    
    experiments = [
        {
            "name": "Baseline (No Overlap)",
            "cmd": base_cmd  # 没有--overlap，没有自匹配问题
        },
        {
            "name": "Overlap + No LOO", 
            "cmd": base_cmd + " --overlap"  # 有overlap，有自匹配问题
        },
        {
            "name": "Overlap + Simple LOO",
            "cmd": base_cmd + " --overlap --exclude_self"  # 有overlap，用LOO解决
        }
    ]
    
    results = {}
    
    for exp in experiments:
        success, duration, output = run_single_experiment(exp["name"], exp["cmd"])
        
        if success:
            result_file = find_latest_result()
            parsed = parse_result(result_file)
            
            if parsed:
                results[exp["name"]] = {
                    **parsed,
                    'duration': duration
                }
                print(f"📈 Image AUROC: {parsed['image_auroc']:.4f}")
            else:
                print("⚠️  Could not parse results")
    
    # 打印最终对比
    print("\n" + "="*70)
    print("FINAL COMPARISON RESULTS")
    print("="*70)
    
    if len(results) >= 2:
        print(f"{'Experiment':<25} {'Image AUROC':<12} {'Time(s)':<8} {'vs Baseline':<12}")
        print("-" * 70)
        
        baseline_auc = None
        overlap_no_loo_auc = None
        
        for name, result in results.items():
            img_auc = result['image_auroc']
            duration = result['duration']
            
            if "Baseline" in name:
                baseline_auc = img_auc
                comparison = "Baseline"
            elif "No LOO" in name:
                overlap_no_loo_auc = img_auc
                if baseline_auc:
                    diff = img_auc - baseline_auc
                    comparison = f"{diff:+.4f}"
                else:
                    comparison = "N/A"
            else:  # LOO
                if overlap_no_loo_auc:
                    diff = img_auc - overlap_no_loo_auc
                    comparison = f"{diff:+.4f}"
                elif baseline_auc:
                    diff = img_auc - baseline_auc
                    comparison = f"{diff:+.4f}"
                else:
                    comparison = "N/A"
            
            print(f"{name:<25} {img_auc:<12.4f} {duration:<8.1f} {comparison:<12}")
        
        print("\n💡 Expected results:")
        print("   - Baseline vs Overlap+NoLOO: Overlap应该更差（负值）")
        print("   - Overlap+NoLOO vs Overlap+LOO: LOO应该更好（正值）")
        
    else:
        print("Not enough successful experiments for comparison")
    
    print("="*70)

if __name__ == "__main__":
    main()
