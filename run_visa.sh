##################### BTAD
datapath=../ITD
datasets=()
dataset_flags=($(for dataset in "${datasets[@]}"; do echo '-d '$dataset; done))

python main.py --dataset visa --data_path ./visa --noise 0.08  "${dataset_flags[@]}" --seed 0 --gpu 0 --resize 512 --imagesize 512 --sampling_ratio 0.01 --subdatasets candle --subdatasets capsules --subdatasets cashew --subdatasets chewinggum --subdatasets fryum --subdatasets macaroni1 --subdatasets macaroni2 --subdatasets pcb1 --subdatasets pcb2 --subdatasets pcb3 --subdatasets pcb4 --subdatasets pipe_fryum