#!/usr/bin/env python3
"""
简单演示LOO-Coreset的核心概念
"""

import numpy as np

def demo_simple_loo():
    """演示简单LOO的核心思想"""
    
    print("="*60)
    print("SIMPLE LOO-CORESET DEMO")
    print("="*60)
    
    # 模拟场景
    print("\n1. 模拟训练数据：")
    print("   - 3张训练图像，每张有2个patch")
    print("   - 记忆库包含所有6个patch")
    
    # 训练图像到patch的映射
    image_to_patches = {
        0: (0, 2),  # 图像0的patch: 0,1
        1: (2, 4),  # 图像1的patch: 2,3  
        2: (4, 6),  # 图像2的patch: 4,5
    }
    
    # 模拟记忆库特征（6个patch）
    memory_bank = np.array([
        [1.0, 0.0],  # patch 0 (图像0)
        [1.1, 0.1],  # patch 1 (图像0)
        [2.0, 0.0],  # patch 2 (图像1)
        [2.1, 0.1],  # patch 3 (图像1)
        [3.0, 0.0],  # patch 4 (图像2)
        [3.1, 0.1],  # patch 5 (图像2)
    ])
    
    print(f"   记忆库形状: {memory_bank.shape}")
    print(f"   图像到patch映射: {image_to_patches}")
    
    # 模拟overlap测试场景
    print("\n2. Overlap测试场景：")
    print("   - 测试图像就是训练图像1（包含patch 2,3）")
    
    test_image_idx = 1
    test_patches = np.array([
        [2.0, 0.0],  # 测试patch 0 (对应训练patch 2)
        [2.1, 0.1],  # 测试patch 1 (对应训练patch 3)
    ])
    
    print(f"   测试图像索引: {test_image_idx}")
    print(f"   测试patch: {test_patches}")
    
    # 标准方法：会找到自己
    print("\n3. 标准方法（会找到自己）：")
    for i, test_patch in enumerate(test_patches):
        distances = np.linalg.norm(memory_bank - test_patch, axis=1)
        nearest_idx = np.argmin(distances)
        nearest_distance = distances[nearest_idx]
        
        print(f"   测试patch {i}: 最近邻是记忆库patch {nearest_idx}, 距离={nearest_distance:.3f}")
        if nearest_distance < 0.01:
            print(f"   ⚠️  找到了自己！距离≈0，异常分数被压制")
    
    # LOO方法：排除自己
    print("\n4. LOO方法（排除自己）：")
    exclude_start, exclude_end = image_to_patches[test_image_idx]
    print(f"   排除记忆库patch范围: [{exclude_start}, {exclude_end})")
    
    for i, test_patch in enumerate(test_patches):
        distances = np.linalg.norm(memory_bank - test_patch, axis=1)
        
        # 排除自己的patch
        valid_indices = [j for j in range(len(distances)) if not (exclude_start <= j < exclude_end)]
        valid_distances = distances[valid_indices]
        
        if len(valid_distances) > 0:
            nearest_valid_idx = valid_indices[np.argmin(valid_distances)]
            nearest_distance = np.min(valid_distances)
            
            print(f"   测试patch {i}: 最近邻是记忆库patch {nearest_valid_idx}, 距离={nearest_distance:.3f}")
            print(f"   ✅ 成功排除了自己，距离 > 0")
        else:
            print(f"   测试patch {i}: 所有邻居都被排除！")
    
    print("\n5. 效果对比：")
    print("   标准方法: 距离≈0 → 异常分数被压制 → 漏检")
    print("   LOO方法:  距离>0 → 正常异常分数 → 正确检测")
    
    print("\n6. 复杂度分析：")
    print("   - 记忆库构建: 1次（与标准方法相同）")
    print("   - 预测时: 只需要简单的索引过滤")
    print("   - 总复杂度: 几乎与标准方法相同！")
    
    print("\n" + "="*60)
    print("结论: 简单LOO只需要在预测时排除对应的记忆库patch，")
    print("      不需要重新构建记忆库，复杂度很低！")
    print("="*60)

if __name__ == "__main__":
    demo_simple_loo()
