import logging
import os
import pickle
import tqdm

import torch
# import common
# import sampler
from .common import NetworkFeatureAggregator, Preprocessing,FaissNN,Aggregator,RescaleSegmentor,NearestNeighbourScorer
from .sampler import ApproximateGreedyCoresetSampler,WeightedGreedyCoresetSampler
# import multi_variate_gaussian
from .multi_variate_gaussian import MultiVariateGaussian
from sklearn.neighbors import LocalOutlierFactor
# import backbones
import torch.nn.functional as F
import numpy as np
from .backbones import load
# from torch_cluster import graclus_cluster
import matplotlib.pyplot as plt

LOGGER = logging.getLogger(__name__)


class SoftPatch(torch.nn.Module):
    def __init__(self, device):
        super(SoftPatch, self).__init__()
        self.device = device

    def load(
        self,
        backbone,
        device,
        input_shape,
        layers_to_extract_from=("layer2", "layer2"),
        pretrain_embed_dimension=1024,
        target_embed_dimension=1024,
        patchsize=3,
        patchstride=1,
        anomaly_score_num_nn=1,
        featuresampler=ApproximateGreedyCoresetSampler(percentage=0.1, device=torch.device("cuda")),
        nn_method=FaissNN(False, 4),
        lof_k=5,
        threshold=0.15,
        weight_method="lof",
        soft_weight_flag=True,
        exclude_self=False,
        similarity_threshold=0.01,
        fast_loo=False,
        **kwargs,
    ):
        self.backbone = backbone.to(device)
        self.layers_to_extract_from = layers_to_extract_from
        self.input_shape = input_shape

        self.device = device
        self.patch_maker = PatchMaker(patchsize, stride=patchstride)

        self.forward_modules = torch.nn.ModuleDict({})

        feature_aggregator = NetworkFeatureAggregator(
            self.backbone, self.layers_to_extract_from, self.device
        )
        feature_dimensions = feature_aggregator.feature_dimensions(input_shape)
        self.forward_modules["feature_aggregator"] = feature_aggregator

        preprocessing = Preprocessing(feature_dimensions, pretrain_embed_dimension)
        self.forward_modules["preprocessing"] = preprocessing

        self.target_embed_dimension = target_embed_dimension
        preadapt_aggregator = Aggregator(
            target_dim=target_embed_dimension
        )

        _ = preadapt_aggregator.to(device)
        self.forward_modules["preadapt_aggregator"] = preadapt_aggregator

        self.anomaly_scorer = NearestNeighbourScorer(
            n_nearest_neighbours=anomaly_score_num_nn, nn_method=nn_method
        )

        self.anomaly_segmentor = RescaleSegmentor(
            device=device, target_size=input_shape[-2:]
        )

        self.featuresampler = featuresampler

        ############SoftPatch ##########
        self.featuresampler = WeightedGreedyCoresetSampler(featuresampler.percentage,
                                                                   featuresampler.device)
        self.patch_weight = None
        self.feature_shape = []
        self.lof_k = lof_k
        self.threshold = threshold
        self.coreset_weight = None
        self.weight_method = weight_method
        self.soft_weight_flag = soft_weight_flag
        
        # 新增LOO相关参数
        self.exclude_self = exclude_self
        self.similarity_threshold = similarity_threshold
        self.fast_loo = fast_loo
        self.training_features = None

    def embed(self, data):
        if isinstance(data, torch.utils.data.DataLoader):
            features = []
            for image in data:
                if isinstance(image, dict):
                    image = image["image"]
                with torch.no_grad():
                    input_image = image.to(torch.float).to(self.device)
                    features.append(self._embed(input_image))
            return features
        return self._embed(data)

    def _embed(self, images, detach=True, provide_patch_shapes=False):
        """Returns feature embeddings for images."""

        def _detach(features):
            if detach:
                return [x.detach().cpu().numpy() for x in features]
            return features

        _ = self.forward_modules["feature_aggregator"].eval()
        with torch.no_grad():
            features = self.forward_modules["feature_aggregator"](images)

        features = [features[layer] for layer in self.layers_to_extract_from]

        features = [
            self.patch_maker.patchify(x, return_spatial_info=True) for x in features
        ]
        patch_shapes = [x[1] for x in features]
        features = [x[0] for x in features]
        ref_num_patches = patch_shapes[0]

        for i in range(1, len(features)):
            _features = features[i]
            patch_dims = patch_shapes[i]

            _features = _features.reshape(
                _features.shape[0], patch_dims[0], patch_dims[1], *_features.shape[2:]
            )
            _features = _features.permute(0, -3, -2, -1, 1, 2)
            perm_base_shape = _features.shape
            _features = _features.reshape(-1, *_features.shape[-2:])
            _features = F.interpolate(
                _features.unsqueeze(1),
                size=(ref_num_patches[0], ref_num_patches[1]),
                mode="bilinear",
                align_corners=False,
            )
            _features = _features.squeeze(1)
            _features = _features.reshape(
                *perm_base_shape[:-2], ref_num_patches[0], ref_num_patches[1]
            )
            _features = _features.permute(0, -2, -1, 1, 2, 3)
            _features = _features.reshape(len(_features), -1, *_features.shape[-3:])
            features[i] = _features
        features = [x.reshape(-1, *x.shape[-3:]) for x in features]

        # As different feature backbones & patching provide differently
        # sized features, these are brought into the correct form here.
        features = self.forward_modules["preprocessing"](features)
        features = self.forward_modules["preadapt_aggregator"](features)

        if provide_patch_shapes:
            return _detach(features), patch_shapes
        return _detach(features)

    def fit(self, training_data):
        """
        This function computes the embeddings of the training data and fills the
        memory bank of SPADE.
        """
        self._fill_memory_bank(training_data)

    def _fill_memory_bank(self, input_data):
        """Computes and sets the support features for SPADE."""
        _ = self.forward_modules.eval()

        def _image_to_features(input_image):
            with torch.no_grad():
                input_image = input_image.to(torch.float).to(self.device)
                return self._embed(input_image)

        # 收集所有特征和对应的图像索引
        all_features = []
        image_to_patches = {}  # 记录每个图像对应的patch索引范围
        patch_start_idx = 0

        with tqdm.tqdm(
            input_data, desc="Computing support features...", leave=True
        ) as data_iterator:
            for img_idx, image in enumerate(data_iterator):
                if isinstance(image, dict):
                    image = image["image"]
                img_features = _image_to_features(image)
                all_features.append(img_features)

                # 记录当前图像的patch范围
                patch_end_idx = patch_start_idx + len(img_features)
                image_to_patches[img_idx] = (patch_start_idx, patch_end_idx)
                patch_start_idx = patch_end_idx

        features = np.concatenate(all_features, axis=0)

        # 保存训练数据信息用于LOO
        if self.exclude_self:
            self.training_features = features.copy()
            self.image_to_patches = image_to_patches
            self.total_images = len(image_to_patches)
            LOGGER.info(f"LOO-Coreset enabled: {self.total_images} training images, {len(features)} total patches")

        # 如果不使用LOO，按原来的方式构建记忆库
        if not self.exclude_self:
            self._build_standard_memory_bank(features, image)
        else:
            # LOO模式：预计算权重但不构建记忆库
            self._prepare_loo_weights(features, image)

    def _build_standard_memory_bank(self, features, last_image):
        """构建标准记忆库（非LOO模式）"""
        with torch.no_grad():
            self.feature_shape = self._embed(last_image.to(torch.float).to(self.device), provide_patch_shapes=True)[1][0]
            patch_weight = self._compute_patch_weight(features)
            patch_weight = patch_weight.reshape(-1)
            threshold = torch.quantile(patch_weight, 1 - self.threshold)
            sampling_weight = torch.where(patch_weight > threshold, 0, 1)
            self.featuresampler.set_sampling_weight(sampling_weight)
            self.patch_weight = patch_weight.clamp(min=0)

            sample_features, sample_indices = self.featuresampler.run(features)
            features = sample_features
            self.coreset_weight = self.patch_weight[sample_indices].cpu().numpy()

        self.anomaly_scorer.fit(detection_features=[features])

    def _prepare_loo_weights(self, features, last_image):
        """为LOO模式预计算权重"""
        with torch.no_grad():
            self.feature_shape = self._embed(last_image.to(torch.float).to(self.device), provide_patch_shapes=True)[1][0]
            patch_weight = self._compute_patch_weight(features)
            patch_weight = patch_weight.reshape(-1)
            self.patch_weight = patch_weight.clamp(min=0)

        # LOO模式下不预先构建记忆库，而是在预测时动态构建

    def set_test_image_mapping(self, test_to_train_mapping):
        """设置测试图像到训练图像的映射（用于overlap场景）"""
        self.test_image_mapping = test_to_train_mapping
        LOGGER.info(f"Set test-to-train mapping for {len(test_to_train_mapping)} test images")

    def _compute_patch_weight(self, features: np.ndarray):
        if isinstance(features, np.ndarray):
            features = torch.from_numpy(features)

        reduced_features = self.featuresampler._reduce_features(features)
        patch_features = \
            reduced_features.reshape(-1, self.feature_shape[0]*self.feature_shape[1], reduced_features.shape[-1])

        # if aligned:
        #     codebook = patch_features[0]
        #     assign = []
        #     for i in range(1, patch_features.shape[0]):
        #         dist = torch.cdist(codebook, patch_features[i]).cpu().numpy()
        #         row_ind, col_ind = linear_assignment(dist)
        #         assign.append(col_ind)
        #         patch_features[i]=torch.index_select(patch_features[i], 0, torch.from_numpy(col_ind).to(self.device))

        patch_features = patch_features.permute(1, 0, 2)

        if self.weight_method == "lof":
            patch_weight = self._compute_lof(self.lof_k, patch_features).transpose(-1, -2)
        elif self.weight_method == "lof_gpu":
            patch_weight = self._compute_lof_gpu(self.lof_k, patch_features).transpose(-1, -2)
        elif self.weight_method == "nearest":
            patch_weight = self._compute_nearest_distance(patch_features).transpose(-1, -2)
            patch_weight = patch_weight + 1
        elif self.weight_method == "gaussian":
            gaussian = MultiVariateGaussian(patch_features.shape[2], patch_features.shape[0])
            stats = gaussian.fit(patch_features)
            patch_weight = self._compute_distance_with_gaussian(patch_features, stats).transpose(-1, -2)
            patch_weight = patch_weight + 1
        else:
            raise ValueError("Unexpected weight method")

        # if aligned:
        #     patch_weight = patch_weight.cpu().numpy()
        #     for i in range(0, patch_weight.shape[0]):
        #         patch_weight[i][assign[i]] = patch_weight[i]
        #     patch_weight = torch.from_numpy(patch_weight).to(self.device)

        return patch_weight

   # def _compute_distance_with_gaussian(self, embedding: torch.Tensor, stats: [torch.Tensor]) -> torch.Tensor:
    def _compute_distance_with_gaussian(self, embedding: torch.Tensor, stats: list[torch.Tensor]) -> torch.Tensor:
        """
        Args:
            embedding (Tensor): Embedding Vector
            stats (List[Tensor]): Mean and Covariance Matrix of the multivariate Gaussian distribution

        Returns:
            Anomaly score of a test image via mahalanobis distance.
        """
        # patch, batch, channel = embedding.shape
        embedding = embedding.permute(1, 2, 0)

        mean, inv_covariance = stats
        delta = (embedding - mean).permute(2, 0, 1)

        distances = (torch.matmul(delta, inv_covariance) * delta).sum(2)
        distances = torch.sqrt(distances)

        return distances

    def _compute_nearest_distance(self, embedding: torch.Tensor) -> torch.Tensor:
        patch, batch, _ = embedding.shape

        x_x = (embedding ** 2).sum(dim=-1, keepdim=True).expand(patch, batch, batch)
        dist_mat = (x_x + x_x.transpose(-1, -2) - 2 * embedding.matmul(embedding.transpose(-1, -2))).abs() ** 0.5
        nearest_distance = torch.topk(dist_mat, dim=-1, largest=False, k=2)[0].sum(dim=-1)  #
        # nearest_distance = nearest_distance.transpose(0, 1).reshape(batch * patch)
        return nearest_distance

    def _compute_lof(self, k, embedding: torch.Tensor) -> torch.Tensor:
        patch, batch, _ = embedding.shape   # 784x219x128
        clf = LocalOutlierFactor(n_neighbors=int(k), metric='l2')
        scores = torch.zeros(size=(patch, batch), device=embedding.device)
        for i in range(patch):
            clf.fit(embedding[i].cpu())
            scores[i] = torch.Tensor(- clf.negative_outlier_factor_)
            # scores[i] = scores[i] / scores[i].mean()   # normalization
        # embedding = embedding.reshape(patch*batch, channel)
        # clf.fit(embedding.cpu())
        # scores = torch.Tensor(- clf.negative_outlier_factor_)
        # scores = scores.reshape(patch, batch)
        return scores

    def _compute_lof_gpu(self, k, embedding: torch.Tensor) -> torch.Tensor:
        """
        GPU support
        """

        patch, batch, _ = embedding.shape

        # calculate distance
        x_x = (embedding ** 2).sum(dim=-1, keepdim=True).expand(patch, batch, batch)
        dist_mat = (x_x + x_x.transpose(-1, -2) - 2 * embedding.matmul(embedding.transpose(-1, -2))).abs() ** 0.5 + 1e-6

        # find neighborhoods
        top_k_distance_mat, top_k_index = torch.topk(dist_mat, dim=-1, largest=False, k=k + 1)
        top_k_distance_mat, top_k_index = top_k_distance_mat[:, :, 1:], top_k_index[:, :, 1:]
        k_distance_value_mat = top_k_distance_mat[:, :, -1]

        # calculate reachability distance
        reach_dist_mat = torch.max(dist_mat, k_distance_value_mat.unsqueeze(2).expand(patch, batch, batch)
                                   .transpose(-1, -2))  # Transposing is important
        top_k_index_hot = torch.zeros(size=dist_mat.shape, device=top_k_index.device).scatter_(-1, top_k_index, 1)

        # Local reachability density
        lrd_mat = k / (top_k_index_hot * reach_dist_mat).sum(dim=-1)

        # calculate local outlier factor
        lof_mat = ((lrd_mat.unsqueeze(2).expand(patch, batch, batch).transpose(-1, -2) * top_k_index_hot).sum(
            dim=-1) / k) / lrd_mat
        return lof_mat


    def _chunk_lof(self, k, embedding: torch.Tensor) -> torch.Tensor:
        width, height, batch, channel = embedding.shape
        chunk_size = 2

        new_width, new_height = int(width / chunk_size), int(height / chunk_size)
        new_patch = new_width * new_height
        new_batch = batch * chunk_size * chunk_size

        split_width = torch.stack(embedding.split(chunk_size, dim=0), dim=0)
        split_height = torch.stack(split_width.split(chunk_size, dim=1 + 1), dim=1)

        new_embedding = split_height.view(new_patch, new_batch, channel)
        lof_mat = self._compute_lof(k, new_embedding)
        chunk_lof_mat = lof_mat.reshape(new_width, new_height, chunk_size, chunk_size, batch)
        chunk_lof_mat = chunk_lof_mat.transpose(1, 2).reshape(width, height, batch)
        return chunk_lof_mat

    def predict(self, data):
        if isinstance(data, torch.utils.data.DataLoader):
            return self._predict_dataloader(data)
        return self._predict(data)

    def _predict_dataloader(self, dataloader):
        """This function provides anomaly scores/maps for full dataloaders."""
        _ = self.forward_modules.eval()

        scores = []
        masks = []
        labels_gt = []
        masks_gt = []
        
        
        with tqdm.tqdm(dataloader, desc="Inferring...", leave=True) as data_iterator:
            for image in data_iterator:
                if isinstance(image, dict):
                    labels_gt.extend(image["is_anomaly"].numpy().tolist())
                    masks_gt.extend(image["mask"].numpy().tolist())
                    image = image["image"]
                _scores, _masks = self._predict(image)
                for score, mask in zip(_scores, _masks):
                    scores.append(score)
                    masks.append(mask)
        return scores, masks, labels_gt, masks_gt

    def _compute_similarity(self, query_features, memory_features):
        """计算查询特征与记忆库特征的相似度"""
        from sklearn.metrics.pairwise import cosine_similarity
        
        # 使用余弦相似度计算
        similarity_matrix = cosine_similarity(query_features, memory_features)
        return similarity_matrix

    def _get_valid_nearest_neighbors(self, query_features):
        """获取排除高相似度后的有效最近邻"""
        # 获取所有距离和索引
        all_distances, _, _ = self.anomaly_scorer.predict([query_features])
        
        if not self.exclude_self or self.training_features is None:
            return all_distances
        
        # 获取记忆库特征（已采样的）
        memory_features = self.anomaly_scorer.detection_features[0]
        memory_similarity = self._compute_similarity(query_features, memory_features)
        
        valid_scores = []
        for i in range(len(query_features)):
            # 找到当前查询patch的所有距离
            patch_distances = all_distances[i] if len(all_distances.shape) > 1 else [all_distances[i]]
            patch_similarities = memory_similarity[i]
            
            # 排除相似度过高的邻居
            valid_mask = patch_similarities < self.similarity_threshold
            
            if np.any(valid_mask):
                # 有有效邻居，选择最近的
                valid_distances = np.array(patch_distances)[valid_mask] if hasattr(patch_distances, '__len__') else patch_distances
                if hasattr(valid_distances, '__len__') and len(valid_distances) > 0:
                    valid_scores.append(np.min(valid_distances))
                else:
                    valid_scores.append(valid_distances)
            else:
                # 所有邻居都被排除，使用次近邻策略
                if hasattr(patch_distances, '__len__') and len(patch_distances) > 1:
                    valid_scores.append(np.partition(patch_distances, 1)[1])
                else:
                    valid_scores.append(patch_distances[0] if hasattr(patch_distances, '__len__') else patch_distances)
        
        return np.array(valid_scores)

    def _build_loo_memory_bank(self, exclude_image_idx):
        """为指定图像构建LOO记忆库（排除该图像的所有patch）"""
        if not hasattr(self, 'image_to_patches'):
            raise ValueError("LOO mode requires image_to_patches mapping")

        # 获取要排除的patch索引范围
        exclude_start, exclude_end = self.image_to_patches[exclude_image_idx]

        # 创建排除掩码
        total_patches = len(self.training_features)
        include_mask = np.ones(total_patches, dtype=bool)
        include_mask[exclude_start:exclude_end] = False

        # 构建LOO特征集
        loo_features = self.training_features[include_mask]
        loo_weights = self.patch_weight[include_mask].cpu().numpy()

        # 应用采样权重
        threshold = torch.quantile(self.patch_weight, 1 - self.threshold)
        sampling_weight = torch.where(self.patch_weight > threshold, 0, 1)
        loo_sampling_weight = sampling_weight[include_mask]

        # 使用加权采样器
        self.featuresampler.set_sampling_weight(loo_sampling_weight)
        sample_features, sample_indices = self.featuresampler.run(loo_features)

        # 获取对应的权重
        loo_coreset_weight = loo_weights[sample_indices]

        return sample_features, loo_coreset_weight

    def _predict_with_loo(self, query_features, test_image_idx):
        """使用LOO策略进行预测"""
        # 为当前测试图像构建专属记忆库
        loo_memory_features, loo_weights = self._build_loo_memory_bank(test_image_idx)

        # 创建临时的异常检测器
        temp_scorer = NearestNeighbourScorer(
            n_nearest_neighbours=self.anomaly_scorer.n_nearest_neighbours,
            nn_method=self.anomaly_scorer.nn_method
        )
        temp_scorer.fit(detection_features=[loo_memory_features])

        # 进行预测
        image_scores, _, indices = temp_scorer.predict([query_features])

        # 应用软权重
        if self.soft_weight_flag:
            indices = indices.squeeze()
            weight = np.take(loo_weights, axis=0, indices=indices)
            image_scores = image_scores * weight

        return image_scores

    def _predict(self, images):
        """Infer score and mask for a batch of images."""
        images = images.to(torch.float).to(self.device)
        _ = self.forward_modules.eval()

        batchsize = images.shape[0]
        with torch.no_grad():
            features, patch_shapes = self._embed(images, provide_patch_shapes=True)
            features = np.asarray(features)

            # 使用LOO策略或原始策略
            if self.exclude_self and hasattr(self, 'image_to_patches') and not self.fast_loo:
                # 真正的LOO-Coreset预测
                LOGGER.info("Using True LOO-Coreset prediction")
                image_scores = self._predict_with_true_loo(features, batchsize)
            elif self.exclude_self:
                # 快速LOO或相似度过滤策略
                if self.fast_loo:
                    LOGGER.info("Using Fast LOO approximation")
                else:
                    LOGGER.info("Using similarity-based exclusion (fallback)")
                image_scores = self._get_valid_nearest_neighbors(features)
            else:
                # 标准预测
                image_scores, _, indices = self.anomaly_scorer.predict([features])
                if self.soft_weight_flag:
                    indices = indices.squeeze()
                    weight = np.take(self.coreset_weight, axis=0, indices=indices)
                    image_scores = image_scores * weight

            patch_scores = image_scores

            scales = patch_shapes[0]
            patch_scores = patch_scores.reshape(batchsize, scales[0], scales[1])
            masks = self.anomaly_segmentor.convert_to_segmentation(patch_scores)

        return [score for score in image_scores], [mask for mask in masks]

    def _predict_with_true_loo(self, features, batchsize):
        """使用真正的LOO-Coreset进行预测（优化版本）"""
        # 计算每个图像的patch数量
        patches_per_image = len(features) // batchsize
        all_scores = []

        LOGGER.info(f"LOO prediction for {batchsize} images, {patches_per_image} patches each")

        # 预构建所有需要的LOO记忆库（批量优化）
        unique_exclude_indices = set()
        image_to_exclude_mapping = {}

        for img_idx in range(batchsize):
            if hasattr(self, 'test_image_mapping'):
                exclude_image_idx = self.test_image_mapping.get(img_idx, img_idx)
            else:
                exclude_image_idx = img_idx % self.total_images

            unique_exclude_indices.add(exclude_image_idx)
            image_to_exclude_mapping[img_idx] = exclude_image_idx

        LOGGER.info(f"Building {len(unique_exclude_indices)} unique LOO memory banks")

        # 预构建LOO记忆库缓存
        loo_cache = {}
        for exclude_idx in unique_exclude_indices:
            loo_memory_features, loo_weights = self._build_loo_memory_bank(exclude_idx)

            # 创建并训练临时检测器
            temp_scorer = NearestNeighbourScorer(
                n_nearest_neighbours=self.anomaly_scorer.n_nearest_neighbours,
                nn_method=self.anomaly_scorer.nn_method
            )
            temp_scorer.fit(detection_features=[loo_memory_features])

            loo_cache[exclude_idx] = (temp_scorer, loo_weights)

        # 批量预测
        for img_idx in range(batchsize):
            start_idx = img_idx * patches_per_image
            end_idx = (img_idx + 1) * patches_per_image
            img_features = features[start_idx:end_idx]

            exclude_image_idx = image_to_exclude_mapping[img_idx]
            temp_scorer, loo_weights = loo_cache[exclude_image_idx]

            # 快速预测
            image_scores, _, indices = temp_scorer.predict([img_features])

            # 应用软权重
            if self.soft_weight_flag:
                indices = indices.squeeze()
                weight = np.take(loo_weights, axis=0, indices=indices)
                image_scores = image_scores * weight

            all_scores.extend(image_scores)

        return np.array(all_scores)



    @staticmethod
    def _params_file(filepath, prepend=""):
        return os.path.join(filepath, prepend + "params.pkl")

    def save_to_path(self, save_path: str, prepend: str = "") -> None:
        LOGGER.info("Saving data.")
        self.anomaly_scorer.save(
            save_path, save_features_separately=False, prepend=prepend
        )
        params = {
            "backbone.name": self.backbone.name,
            "layers_to_extract_from": self.layers_to_extract_from,
            "input_shape": self.input_shape,
            "pretrain_embed_dimension": self.forward_modules[
                "preprocessing"
            ].output_dim,
            "target_embed_dimension": self.forward_modules[
                "preadapt_aggregator"
            ].target_dim,
            "patchsize": self.patch_maker.patchsize,
            "patchstride": self.patch_maker.stride,
            "anomaly_scorer_num_nn": self.anomaly_scorer.n_nearest_neighbours,
        }
        with open(self._params_file(save_path, prepend), "wb") as save_file:
            pickle.dump(params, save_file, pickle.HIGHEST_PROTOCOL)
from typing import Optional

def load_from_path(
    self,
    load_path: str,
    device: torch.device,
    nn_method: Optional[FaissNN] = None,  # ✅ 正确：使用 Optional 类型
    prepend: str = "",
) -> None:
    LOGGER.info("Loading and initializing.")
    with open(self._params_file(load_path, prepend), "rb") as load_file:
        params = pickle.load(load_file)
    params["backbone"] = load(
        params["backbone.name"]
    )
    params["backbone"].name = params["backbone.name"]
    del params["backbone.name"]

    if nn_method is None:
        nn_method = FaissNN(False, 4)  # ✅ 在函数内部初始化

    self.load(**params, device=device, nn_method=nn_method)

# Image handling classes.
class PatchMaker:
    def __init__(self, patchsize, stride=None):
        self.patchsize = patchsize
        self.stride = stride

    def patchify(self, features, return_spatial_info=False):
        """Convert a tensor into a tensor of respective patches.
        Args:
            x: [torch.Tensor, bs x c x w x h]
        Returns:
            x: [torch.Tensor, bs * w//stride * h//stride, c, patchsize,
            patchsize]
        """
        padding = int((self.patchsize - 1) / 2)
        unfolder = torch.nn.Unfold(
            kernel_size=self.patchsize, stride=self.stride, padding=padding, dilation=1
        )
        unfolded_features = unfolder(features)
        number_of_total_patches = []
        for side in features.shape[-2:]:
            n_patches = (
                side + 2 * padding - 1 * (self.patchsize - 1) - 1
            ) / self.stride + 1
            number_of_total_patches.append(int(n_patches))
        unfolded_features = unfolded_features.reshape(
            *features.shape[:2], self.patchsize, self.patchsize, -1
        )
        unfolded_features = unfolded_features.permute(0, 4, 1, 2, 3)

        if return_spatial_info:
            return unfolded_features, number_of_total_patches
        return unfolded_features

    def unpatch_scores(self, patch_scores, batchsize):
        return patch_scores.reshape(batchsize, -1, *patch_scores.shape[1:])

    def score(self, image_scores):
        was_numpy = False
        if isinstance(image_scores, np.ndarray):
            was_numpy = True
            image_scores = torch.from_numpy(image_scores)
        while image_scores.ndim > 1:
            image_scores = torch.max(image_scores, dim=-1).values
        if was_numpy:
            return image_scores.numpy()
        return image_scores







