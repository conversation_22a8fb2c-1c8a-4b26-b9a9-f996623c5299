#!/usr/bin/env python3
"""
测试真正的LOO-Coreset实现效果
"""

import subprocess
import os
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_experiment(name, cmd):
    """运行实验并返回结果路径"""
    logger.info(f"Running experiment: {name}")
    logger.info(f"Command: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=3600)
        if result.returncode != 0:
            logger.error(f"Experiment {name} failed with return code {result.returncode}")
            logger.error(f"Error output: {result.stderr}")
            return None
        
        logger.info(f"Experiment {name} completed successfully")
        return result.stdout
    except subprocess.TimeoutExpired:
        logger.error(f"Experiment {name} timed out")
        return None
    except Exception as e:
        logger.error(f"Experiment {name} failed with exception: {e}")
        return None

def find_latest_result():
    """找到最新的结果文件"""
    result_dir = "result/project"
    if not os.path.exists(result_dir):
        return None
    
    # 找到最新的group目录
    group_dirs = [d for d in os.listdir(result_dir) if d.startswith("group_")]
    if not group_dirs:
        return None
    
    # 按数字排序，取最大的
    group_nums = [int(d.split("_")[1]) for d in group_dirs if d.split("_")[1].isdigit()]
    if not group_nums:
        return None
    
    latest_group = f"group_{max(group_nums)}"
    result_file = os.path.join(result_dir, latest_group, "results.csv")
    
    if os.path.exists(result_file):
        return result_file
    return None

def parse_results(result_file):
    """解析结果文件"""
    if not result_file or not os.path.exists(result_file):
        return None
    
    try:
        df = pd.read_csv(result_file)
        # 找到Mean行
        mean_row = df[df['Row Names'] == 'Mean']
        if not mean_row.empty:
            return {
                'image_auroc': float(mean_row['image_auroc'].iloc[0]),
                'pixel_auroc': float(mean_row['pixel_auroc'].iloc[0])
            }
    except Exception as e:
        logger.error(f"Failed to parse results from {result_file}: {e}")
    
    return None

def main():
    """主测试函数"""
    
    # 基础命令
    base_cmd = "python main.py --dataset visa --data_path ./visa --noise 0.08 --overlap --seed 0 --gpu 0 --resize 512 --imagesize 512 --sampling_ratio 0.01 --subdatasets candle"
    
    experiments = [
        {
            "name": "Baseline (No LOO)",
            "cmd": base_cmd
        },
        {
            "name": "LOO-Coreset (similarity_threshold=0.01)",
            "cmd": base_cmd + " --exclude_self --similarity_threshold 0.01"
        },
        {
            "name": "LOO-Coreset (similarity_threshold=0.1)",
            "cmd": base_cmd + " --exclude_self --similarity_threshold 0.1"
        },
        {
            "name": "LOO-Coreset (similarity_threshold=0.2)",
            "cmd": base_cmd + " --exclude_self --similarity_threshold 0.2"
        }
    ]
    
    results = {}
    
    for exp in experiments:
        # 运行实验
        output = run_experiment(exp["name"], exp["cmd"])
        if output is None:
            continue
        
        # 获取结果
        result_file = find_latest_result()
        parsed_results = parse_results(result_file)
        
        if parsed_results:
            results[exp["name"]] = parsed_results
            logger.info(f"Results for {exp['name']}: {parsed_results}")
        else:
            logger.warning(f"Could not parse results for {exp['name']}")
    
    # 打印对比结果
    print("\n" + "="*80)
    print("LOO-CORESET COMPARISON RESULTS")
    print("="*80)
    
    if results:
        print(f"{'Experiment':<40} {'Image AUROC':<15} {'Pixel AUROC':<15} {'Improvement':<15}")
        print("-" * 80)
        
        baseline_img_auc = None
        baseline_pixel_auc = None
        
        for name, result in results.items():
            img_auc = result['image_auroc']
            pixel_auc = result['pixel_auroc']
            
            if "Baseline" in name:
                baseline_img_auc = img_auc
                baseline_pixel_auc = pixel_auc
                improvement = "Baseline"
            else:
                if baseline_img_auc is not None:
                    img_improvement = img_auc - baseline_img_auc
                    improvement = f"+{img_improvement:.4f}"
                else:
                    improvement = "N/A"
            
            print(f"{name:<40} {img_auc:<15.4f} {pixel_auc:<15.4f} {improvement:<15}")
    
    else:
        print("No results to compare!")
    
    print("="*80)

if __name__ == "__main__":
    main()
